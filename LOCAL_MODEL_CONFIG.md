# Langflow连接本地vLLM配置示例

## 1. vLLM服务端启动

```bash
# 方式1：基本启动
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-7B-Instruct \
    --host 0.0.0.0 \
    --port 8000

# 方式2：带API密钥启动
python -m vllm.entrypoints.openai.api_server \
    --model Qwen/Qwen2.5-7B-Instruct \
    --host 0.0.0.0 \
    --port 8000 \
    --api-key sk-local-vllm-key

# 方式3：指定本地模型路径
python -m vllm.entrypoints.openai.api_server \
    --model /path/to/local/model \
    --host 0.0.0.0 \
    --port 8000
```

## 2. Langflow配置

### OpenAI组件配置：
- **Model Provider**: OpenAI
- **OpenAI API Base**: `http://localhost:8000/v1`
- **OpenAI API Key**: `sk-local-vllm-key` (或任意字符串)
- **Model Name**: `Qwen/Qwen2.5-7B-Instruct` (与vLLM启动时的模型名一致)

### 高级配置：
- **Temperature**: 0.7
- **Max Tokens**: 2048
- **Timeout**: 60

## 3. 验证连接

### 测试API可用性：
```bash
curl http://localhost:8000/v1/models
```

### 测试聊天接口：
```bash
curl http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer sk-local-vllm-key" \
  -d '{
    "model": "Qwen/Qwen2.5-7B-Instruct",
    "messages": [
      {"role": "user", "content": "Hello!"}
    ]
  }'
```

## 4. 常见问题解决

### 问题1：连接超时
- 检查vLLM服务是否正常运行
- 确认端口8000未被占用
- 检查防火墙设置

### 问题2：模型名称不匹配
- 使用 `curl http://localhost:8000/v1/models` 查看可用模型
- 确保Langflow中的模型名称与vLLM中的完全一致

### 问题3：API密钥错误
- 如果vLLM启动时未设置API密钥，可以使用任意字符串
- 如果设置了API密钥，必须在Langflow中使用相同的密钥

## 5. 其他兼容的本地服务

除了vLLM，还可以连接：
- **Ollama**: `http://localhost:11434/v1`
- **LocalAI**: `http://localhost:8080/v1`
- **Text Generation WebUI**: `http://localhost:5000/v1`
- **LM Studio**: `http://localhost:1234/v1`

## 6. Docker部署vLLM示例

```bash
docker run --gpus all \
    -v ~/.cache/huggingface:/root/.cache/huggingface \
    -p 8000:8000 \
    --ipc=host \
    vllm/vllm-openai:latest \
    --model Qwen/Qwen2.5-7B-Instruct \
    --api-key sk-local-vllm-key
```
