# Langflow 前后端分离开发指南

## 🚀 快速开始

### 方式一：使用简化启动器（推荐）

```bash
# 启动开发启动器
python start_dev_simple.py
```

然后按照菜单选择：
- **选择 1**: 启动后端服务 (http://localhost:7860)
- **选择 2**: 启动前端服务 (http://localhost:3000) 
- **选择 3**: 查看详细使用说明

### 方式二：手动分别启动

#### 1️⃣ 启动后端（终端1）
```bash
# 激活虚拟环境
source .venv/Scripts/activate

# 启动后端API服务
python -m uvicorn langflow.main:create_app --factory --host 0.0.0.0 --port 7860 --reload
```

#### 2️⃣ 启动前端（终端2）
```bash
# 进入前端目录
cd src/frontend

# 安装依赖（首次运行）
npm install

# 启动前端开发服务器
npm start
```

## 📍 访问地址

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:7860
- **API文档**: http://localhost:7860/docs

## ✨ 开发优势

### 🔥 热重载支持
- **前端**: 修改React代码自动刷新页面
- **后端**: 修改Python代码自动重启服务

### 🔧 独立开发
- **前端开发**: 专注UI/UX，API请求自动代理到后端
- **后端开发**: 专注业务逻辑，可独立测试API
- **并行开发**: 前后端可同时开发，互不影响

### 🐛 调试便利
- **前端调试**: 使用浏览器开发者工具
- **后端调试**: 使用IDE断点调试
- **网络调试**: 查看API请求/响应

## 📁 项目结构

```
langflow/
├── src/
│   ├── backend/          # 后端代码
│   │   └── base/
│   │       └── langflow/
│   └── frontend/         # 前端代码
│       ├── src/
│       ├── package.json
│       └── vite.config.mts
├── start_backend.py      # 后端启动脚本
├── start_frontend.py     # 前端启动脚本
├── start_dev_simple.py   # 简化启动器
└── README_DEV.md         # 本文档
```

## 🔧 配置说明

### 前端配置 (vite.config.mts)
- **开发端口**: 3000
- **API代理**: 自动代理 `/api/*` 到 `http://localhost:7860`
- **热重载**: 自动启用

### 后端配置
- **服务端口**: 7860
- **CORS**: 允许所有来源（开发模式）
- **热重载**: 使用 `--reload` 参数

## 🛠️ 常用命令

### 后端相关
```bash
# 检查依赖冲突
pip check

# 查看已安装包
pip list

# 启动后端（开发模式）
python start_backend.py
```

### 前端相关
```bash
# 安装依赖
cd src/frontend && npm install

# 启动开发服务器
cd src/frontend && npm start

# 构建生产版本
cd src/frontend && npm run build

# 类型检查
cd src/frontend && npm run type-check
```

## 🔍 故障排除

### 端口冲突
如果端口被占用：
```bash
# 查看端口占用
netstat -ano | findstr :7860
netstat -ano | findstr :3000

# 杀死进程（Windows）
taskkill /PID <PID> /F
```

### 依赖问题
```bash
# 重新安装Python依赖
pip install --force-reinstall -r requirements.txt

# 重新安装前端依赖
cd src/frontend
rm -rf node_modules package-lock.json
npm install
```

### 代理问题
如果前端无法访问后端API：
1. 确认后端在 http://localhost:7860 运行
2. 检查 `src/frontend/vite.config.mts` 中的代理配置
3. 查看浏览器控制台的网络请求

## 📝 开发工作流

### 典型开发流程
1. **启动后端**: `python start_dev_simple.py` → 选择 1
2. **启动前端**: 新终端 → `python start_dev_simple.py` → 选择 2
3. **开始开发**: 访问 http://localhost:3000
4. **修改代码**: 保存后自动重载
5. **测试功能**: 实时查看效果

### 组件开发
1. **后端组件**: 修改 `src/backend/base/langflow/components/`
2. **前端组件**: 修改 `src/frontend/src/`
3. **API接口**: 修改 `src/backend/base/langflow/api/`

## 🎯 最佳实践

1. **始终使用前后端分离模式开发**
2. **前端修改实时预览，提高开发效率**
3. **后端API独立测试，确保接口稳定**
4. **使用浏览器开发者工具调试前端**
5. **使用IDE断点调试后端逻辑**

---

**Happy Coding! 🎉**
