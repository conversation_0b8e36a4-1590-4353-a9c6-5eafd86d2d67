<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Langflow Chat 预览</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 2.5em;
            font-weight: 700;
        }

        .header p {
            margin: 0;
            font-size: 1.1em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .info-section {
            background: #f8fafc;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 30px;
            border-left: 4px solid #4f46e5;
        }

        .info-section h3 {
            margin: 0 0 15px 0;
            color: #1e293b;
            font-size: 1.3em;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .info-item {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e2e8f0;
        }

        .info-item strong {
            color: #4f46e5;
            display: block;
            margin-bottom: 5px;
        }

        .chat-container {
            background: #ffffff;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 2px solid #e2e8f0;
            min-height: 500px;
            position: relative;
        }

        .chat-title {
            text-align: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f1f5f9;
        }

        .chat-title h2 {
            margin: 0;
            color: #1e293b;
            font-size: 1.8em;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        .status-loading {
            background: #fbbf24;
        }

        .status-ready {
            background: #10b981;
        }

        .status-error {
            background: #ef4444;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .loading-message {
            text-align: center;
            color: #64748b;
            font-style: italic;
            margin-top: 50px;
        }

        .troubleshooting {
            background: #fef3c7;
            border: 1px solid #f59e0b;
            border-radius: 8px;
            padding: 20px;
            margin-top: 30px;
        }

        .troubleshooting h4 {
            margin: 0 0 15px 0;
            color: #92400e;
        }

        .troubleshooting ul {
            margin: 0;
            padding-left: 20px;
        }

        .troubleshooting li {
            margin-bottom: 8px;
            color: #92400e;
        }

        .code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            overflow-x: auto;
            margin: 15px 0;
        }

        .footer {
            text-align: center;
            padding: 20px;
            color: #64748b;
            border-top: 1px solid #e2e8f0;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .content {
                padding: 20px;
            }
            
            .info-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 Langflow Chat 预览</h1>
            <p>嵌入式聊天组件测试页面</p>
        </div>

        <div class="content">
            <div class="info-section">
                <h3>📋 组件配置信息</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>窗口标题:</strong>
                        Basic Prompting
                    </div>
                    <div class="info-item">
                        <strong>Flow ID:</strong>
                        dd18c755-dbca-4ea0-b5df-18cdc471da66
                    </div>
                    <div class="info-item">
                        <strong>主机地址:</strong>
                        http://localhost:3000
                    </div>
                    <div class="info-item">
                        <strong>组件版本:</strong>
                        v1.0.7
                    </div>
                </div>
            </div>

            <div class="chat-container">
                <div class="chat-title">
                    <h2>
                        <span id="status-indicator" class="status-indicator status-loading"></span>
                        Langflow 聊天组件
                    </h2>
                    <div style="margin-top: 15px;">
                        <button id="toggle-mode" onclick="toggleMode()" style="
                            background: #4f46e5;
                            color: white;
                            border: none;
                            padding: 8px 16px;
                            border-radius: 6px;
                            cursor: pointer;
                            font-size: 0.9em;
                        ">
                            切换到传统模式 (7860)
                        </button>
                        <span id="current-mode" style="margin-left: 10px; color: #64748b; font-size: 0.9em;">
                            当前：开发模式 (3000)
                        </span>
                    </div>
                </div>

                <!-- Langflow 嵌入式聊天组件 -->
                <!-- 开发模式（前后端分离）：使用 http://localhost:3000 -->
                <!-- 传统模式（一体化启动）：使用 http://localhost:7860 -->
                <langflow-chat
                    window_title="Basic Prompting"
                    flow_id="dd18c755-dbca-4ea0-b5df-18cdc471da66"
                    host_url="http://localhost:3000">
                </langflow-chat>

                <div id="loading-message" class="loading-message">
                    正在加载聊天组件...
                </div>
            </div>

            <div class="troubleshooting">
                <h4>🔧 故障排除</h4>
                <p><strong>开发模式 (端口3000)</strong>：</p>
                <ul>
                    <li>启动前端：<code>cd src/frontend && npm start</code></li>
                    <li>启动后端：<code>python start_backend.py</code></li>
                    <li>访问地址：<strong>http://localhost:3000</strong></li>
                </ul>

                <p><strong>传统模式 (端口7860)</strong>：</p>
                <ul>
                    <li>一键启动：<code>python start_langflow_sqlite.py</code></li>
                    <li>访问地址：<strong>http://localhost:7860</strong></li>
                </ul>

                <p><strong>通用检查</strong>：</p>
                <ul>
                    <li>确保 Flow ID <code>dd18c755-dbca-4ea0-b5df-18cdc471da66</code> 存在且可访问</li>
                    <li>检查浏览器控制台是否有错误信息</li>
                    <li>确认网络连接正常</li>
                </ul>
            </div>

            <div class="info-section">
                <h3>🔗 相关链接</h3>
                <div class="info-grid">
                    <div class="info-item">
                        <strong>前端界面:</strong>
                        <a href="http://localhost:3000" target="_blank">http://localhost:3000</a>
                    </div>
                    <div class="info-item">
                        <strong>后端API:</strong>
                        <a href="http://localhost:7860" target="_blank">http://localhost:7860</a>
                    </div>
                    <div class="info-item">
                        <strong>API文档:</strong>
                        <a href="http://localhost:7860/docs" target="_blank">http://localhost:7860/docs</a>
                    </div>
                    <div class="info-item">
                        <strong>组件文档:</strong>
                        <a href="https://github.com/logspace-ai/langflow-embedded-chat" target="_blank">GitHub</a>
                    </div>
                </div>
            </div>

            <div class="code">
                <strong>HTML 代码:</strong><br>
                &lt;script src="https://cdn.jsdelivr.net/gh/logspace-ai/langflow-embedded-chat@v1.0.7/dist/build/static/js/bundle.min.js"&gt;&lt;/script&gt;<br>
                &lt;langflow-chat<br>
                &nbsp;&nbsp;window_title="Basic Prompting"<br>
                &nbsp;&nbsp;flow_id="dd18c755-dbca-4ea0-b5df-18cdc471da66"<br>
                &nbsp;&nbsp;host_url="http://localhost:3000"&gt;<br>
                &lt;/langflow-chat&gt;
            </div>
        </div>

        <div class="footer">
            <p>💡 提示：确保 Langflow 前后端服务都在运行中</p>
        </div>
    </div>

    <!-- Langflow 嵌入式聊天组件脚本 -->
    <script src="https://cdn.jsdelivr.net/gh/logspace-ai/langflow-embedded-chat@v1.0.7/dist/build/static/js/bundle.min.js"></script>

    <script>
        let isDevelopmentMode = true; // true = 3000端口, false = 7860端口

        // 切换模式函数
        function toggleMode() {
            isDevelopmentMode = !isDevelopmentMode;
            const chatComponent = document.querySelector('langflow-chat');
            const toggleButton = document.getElementById('toggle-mode');
            const currentModeSpan = document.getElementById('current-mode');
            const statusIndicator = document.getElementById('status-indicator');
            const loadingMessage = document.getElementById('loading-message');

            if (isDevelopmentMode) {
                // 切换到开发模式 (3000)
                chatComponent.setAttribute('host_url', 'http://localhost:3000');
                toggleButton.textContent = '切换到传统模式 (7860)';
                currentModeSpan.textContent = '当前：开发模式 (3000)';
            } else {
                // 切换到传统模式 (7860)
                chatComponent.setAttribute('host_url', 'http://localhost:7860');
                toggleButton.textContent = '切换到开发模式 (3000)';
                currentModeSpan.textContent = '当前：传统模式 (7860)';
            }

            // 重置状态
            statusIndicator.className = 'status-indicator status-loading';
            loadingMessage.textContent = '正在重新加载聊天组件...';
            loadingMessage.style.color = '#64748b';

            // 检查新的连接
            setTimeout(checkConnection, 1000);
        }

        // 检查连接函数
        function checkConnection() {
            const currentUrl = isDevelopmentMode ? 'http://localhost:3000' : 'http://localhost:7860';
            const statusIndicator = document.getElementById('status-indicator');
            const loadingMessage = document.getElementById('loading-message');

            fetch(currentUrl + '/health')
                .then(response => {
                    if (response.ok) {
                        statusIndicator.className = 'status-indicator status-ready';
                        loadingMessage.textContent = `✅ 连接成功 (${isDevelopmentMode ? '开发模式' : '传统模式'})`;
                        loadingMessage.style.color = '#10b981';
                    }
                })
                .catch(error => {
                    statusIndicator.className = 'status-indicator status-error';
                    loadingMessage.textContent = `❌ 无法连接到 ${currentUrl}`;
                    loadingMessage.style.color = '#ef4444';
                });
        }

        // 监控组件加载状态
        document.addEventListener('DOMContentLoaded', function() {
            const statusIndicator = document.getElementById('status-indicator');
            const loadingMessage = document.getElementById('loading-message');
            
            // 检查组件是否加载
            setTimeout(function() {
                const chatComponent = document.querySelector('langflow-chat');
                if (chatComponent && chatComponent.shadowRoot) {
                    statusIndicator.className = 'status-indicator status-ready';
                    loadingMessage.textContent = '✅ 聊天组件已加载';
                    loadingMessage.style.color = '#10b981';
                } else {
                    statusIndicator.className = 'status-indicator status-error';
                    loadingMessage.textContent = '❌ 聊天组件加载失败，请检查配置';
                    loadingMessage.style.color = '#ef4444';
                }
            }, 3000);

            // 检查网络连接
            fetch('http://localhost:3000/health')
                .then(response => {
                    if (response.ok) {
                        console.log('✅ Langflow 前端服务连接正常');
                    }
                })
                .catch(error => {
                    console.warn('⚠️ 无法连接到 Langflow 前端服务:', error);
                    statusIndicator.className = 'status-indicator status-error';
                    loadingMessage.textContent = '❌ 无法连接到 Langflow 服务，请确保前端在运行';
                    loadingMessage.style.color = '#ef4444';
                });
        });
    </script>
</body>
</html>
